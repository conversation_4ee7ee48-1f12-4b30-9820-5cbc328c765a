package app

import (
	"context"
	"fmt"

	"billing_service/model"
)

type DriverBonusRequest struct {
	BonusAmount int `json:"bonus_amount"`
	ChallengeId int `json:"challenge_id,omitempty"`
}

func (a *App) ProcessDriverBonus(ctx context.Context, driverId int, req DriverBonusRequest) (errType string, err error) {
	if req.BonusAmount <= 0 {
		errType = "invalid_amount"
		err = fmt.Errorf("bonus amount must be greater than 0")
		return
	}

	var comment string
	if req.ChallengeId > 0 {
		comment = fmt.Sprintf("Driver bonus payment for challenge %d", req.ChallengeId)
	} else {
		comment = "Driver bonus payment"
	}

	a2cRequest := model.A2CPaymentRequest{
		OrderId:  0,
		DriverId: driverId,
		Amount:   req.BonusAmount,
		Reason:   model.PaymentReasonDriverBonus,
		Comment:  comment,
	}

	// Queue the A2C payment task
	_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: a2cRequest}, nil)
	if err != nil {
		err = fmt.<PERSON><PERSON><PERSON>("failed to queue bonus payment: %v", err)
		return
	}

	return
}
