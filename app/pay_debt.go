package app

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"
	"billing_service/util/provider/atmos"
	"billing_service/util/provider/payme"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
	redis "github.com/redis/go-redis/v9"
)

func (a *App) PayDebt(ctx context.Context, userId, cardId int, userType, cardType string) (errType string, err error) {
	key := fmt.Sprintf("billing_service:user:%d:type:%s:card:%d:pay_debt", userId, userType, cardId)

	val, err := a.redis.SetArgs(a.ctx, key, 1, redis.SetArgs{Get: true, TTL: 1 * time.Minute, Mode: "NX"}).Result()
	if err != nil && err != redis.Nil {
		err = fmt.Errorf("redis get and set: %v", err)
		return
	}

	if val == "" {
		defer func() {
			_ = a.redis.Del(a.ctx, key)
		}()
	} else {
		time.Sleep(5 * time.Second)
	}

	switch cardType {
	case "uzcard", "humo", "payme", "":
		errType, err = a.payDebtPayme(ctx, userId, cardId)
	case "visa", "mastercard":
		if a.repo.GetSysParam("is_atmos_enabled", "").Bool() && userType == "client" {
			errType, err = a.payDebtAtmos(ctx, userId, cardId, userType)
		}
	default:
		errType = "bad_request"
		err = errors.New("unknown card type")
	}

	return
}

func (a *App) payDebtPayme(ctx context.Context, clientId, cardId int) (errType string, err error) {
	debts, err := a.repo.GetDebts(ctx, clientId, "client")
	if err != nil || len(debts) == 0 {
		return
	}

	var (
		receiptId      string
		receiptCreated bool
		isPaid         bool
		inProcess      bool
		amount         int
		orderId        = int(debts[0].OrderId.Int64)
	)

	for _, debt := range debts {
		amount += debt.Amount
	}

	payment, err := a.repo.GetPaymentStatusByOrderId(ctx, orderId, model.PaymentReasonPayDebt)
	if err != nil {
		return
	}

	if payment.IsFinished() {
		err = a.repo.DeleteDebts(ctx, clientId, "client")
		return
	}

	if payment.IsCreated() {
		receiptId = payment.Invoice
		receiptCreated, inProcess, isPaid, err = a.checkAndUpdateClientPaymePayment(ctx, receiptId)
		if err != nil {
			return
		}
		if inProcess {
			return
		}
	}

	if !isPaid {
		var card model.Card
		card, err = a.repo.GetCardById(ctx, cardId, clientId, "client")
		if err != nil {
			return
		}

		if card.Id == 0 {
			errType = "bad_request"
			err = errors.New("the client does not have such a card")
			return
		}

		var balanceEnough bool
		balanceEnough, err = a.payme.CardBalance(ctx, card.PaymeToken.String, amount)
		if err != nil {
			switch err {
			case payme.ErrInsufficientBalance:
				errType = "card_insufficient_balance"
			case payme.ErrCardNotFound, payme.ErrInvalidTokenFormat:
				errType = "card_is_not_valid"
			case payme.ErrCardExpired:
				errType = "card_is_expired"
			default:
				errType = "processing_center_not_available"
			}
			err = fmt.Errorf("check card balance: %v", err)
			return
		}

		if !balanceEnough {
			errType = "card_insufficient_balance"
			err = errors.New("card has insufficient balance")
			return
		}

		if !receiptCreated {
			receiptId, err = a.payme.CreateReceipt(ctx, clientId, orderId, card.Id, amount, "client_debt", "Оплата долга")
			if err != nil {
				err = fmt.Errorf("create receipt: %v", err)
				return
			}
		}

		var state null.Int
		state, err = a.payme.PayReceipt(ctx, card.PaymeToken.String, receiptId)
		if err != nil {
			if err != payme.ErrReceiptsAlreadyPayed {
				switch err {
				case payme.ErrInsufficientBalance:
					errType = "card_insufficient_balance"
				case payme.ErrCardNotFound, payme.ErrInvalidTokenFormat:
					errType = "card_is_not_valid"
				case payme.ErrCardExpired:
					errType = "card_is_expired"
				default:
					errType = "processing_center_not_available"
				}
				err = fmt.Errorf("pay receipt: %v", err)
				return
			}
		} else if state.Int64 != payme.CheckStatusPaid {
			err = fmt.Errorf("pay check state not success: %d", state.Int64)
			return
		}
	}

	err = a.repo.DeleteDebts(ctx, clientId, "client")
	if err != nil {
		return
	}

	err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusFinished)

	return
}

func (a *App) payDebtAtmos(ctx context.Context, userId, cardId int, userType string) (errType string, err error) {
	debts, err := a.repo.GetDebts(ctx, userId, userType)
	if err != nil {
		return
	}

	l := len(debts)
	if l == 0 {
		return
	}

	var (
		amount  int
		orderId = int(debts[0].OrderId.Int64)
	)

	for _, debt := range debts {
		amount += debt.Amount
	}

	payment, err := a.repo.GetPaymentStatusByOrderId(ctx, orderId, model.PaymentReasonPayDebt)
	if err != nil {
		return
	}

	if payment.IsFinished() {
		err = a.repo.DeleteDebts(ctx, userId, userType)
		return
	}

	if payment.IsCreated() {
		var transactionResp atmos.TransactionResponse
		transactionResp, err = a.atmos.GetTransaction(ctx, payment.Invoice)
		if err != nil {
			err = fmt.Errorf("get transaction status: %v", err)
			return
		}

		switch {
		case transactionResp.Payload.IsSuccess():
			err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusFinished)
			if err != nil {
				return
			}
			err = a.repo.DeleteDebts(ctx, userId, userType)
			return
		case transactionResp.Payload.IsPending():
			err = fmt.Errorf("apply transaction pending: %s", transactionResp.Payload.GetTransactionStatus().String())
			return
		default:
			err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelled)
			if err != nil {
				return
			}
		}
	}

	card, err := a.repo.GetCardById(ctx, cardId, userId, "client")
	if err != nil {
		err = fmt.Errorf("get card: %v", err)
		return
	}

	if card.Id == 0 {
		errType = "bad_request"
		err = errors.New("the client does not have such a card")
		return
	}

	if card.AtmosToken.String == "" {
		errType = "card_is_not_valid"
		err = errors.New("card atmos token not found")
		return
	}

	cardDetails, err := a.atmos.GetCardDetails(ctx, card.AtmosToken.String)
	if err != nil {
		errType = "processing_center_not_available"
		err = fmt.Errorf("get card details: %v", err)
		return
	}

	if !cardDetails.Payload.Card.Status || !cardDetails.Payload.Card.Approved {
		errType = "card_is_not_valid"
		err = errors.New("card is not valid or not approved")
		return
	}

	holdRequest := atmos.UklonHoldRequest{
		ExtID:        uuid.NewString(),
		Amount:       atmos.SoumToTiyin(amount), // Convert UZS to tiyin (multiply by 100)
		StoreID:      strconv.Itoa(a.cfg.Atmos.StoreID),
		Account:      strconv.Itoa(userId),
		InvoiceID:    fmt.Sprintf("debt_%d_%s", orderId, uuid.NewString()),
		CardID:       cardDetails.Payload.CardID,
		ClientIPAddr: "127.0.0.1",
	}

	holdResp, err := a.atmos.UklonHold(ctx, holdRequest)
	if err != nil {
		err = fmt.Errorf("hold transaction for paydebt: %v", err)
		return
	}

	switch {
	case holdResp.Payload.IsSuccess():
		break
	case holdResp.Payload.IsPending():
		a.log.Errorf("atmos paydebt response body %+v", holdResp)
		err = fmt.Errorf("hold transaction pending response: %s", holdResp.Payload.GetTransactionStatus().String())
		return
	case holdResp.Payload.IsFailed():
		a.log.Errorf("atmos paydebt response body %+v", holdResp)
		err = fmt.Errorf("hold transaction failed for paydebt: %s", holdResp.Payload.GetTransactionStatus().String())
		return
	default:
		a.log.Errorf("atmos paydebt response body %+v", holdResp)
		err = fmt.Errorf("hold transaction failed for paydebt: %s", holdResp.Payload.GetTransactionStatus().String())
		return
	}

	transactionID := strconv.Itoa(holdResp.Payload.ID)

	payment = model.Payment{
		UserId:     userId,
		UserType:   userType,
		OrderId:    null.IntFrom(int64(orderId)),
		CardId:     null.IntFrom(int64(card.Id)),
		Amount:     amount,
		Status:     model.PaymentStatusPending,
		Reason:     model.PaymentReasonPayDebt,
		Invoice:    transactionID,
		ProviderId: model.ProviderIdAtmos,
	}

	err = a.repo.CreatePayment(ctx, payment)
	if err != nil {
		return
	}

	applyResp, err := a.atmos.UklonApply(ctx, transactionID)
	if err != nil {
		err = fmt.Errorf("apply transaction for paydebt: %v", err)
		return
	}

	switch {
	case applyResp.Payload.IsSuccess():
		break
	case applyResp.Payload.IsPending():
		err = fmt.Errorf("apply transaction pending: %s", applyResp.Payload.GetTransactionStatus().String())
		return
	case applyResp.Payload.IsFailed():
		err = fmt.Errorf("apply transaction failed: %s", applyResp.Payload.GetTransactionStatus().String())
		return
	default:
		err = fmt.Errorf("apply transaction failed: %s", applyResp.Payload.GetTransactionStatus().String())
		return
	}

	err = a.repo.UpdatePaymentStatus(ctx, transactionID, model.PaymentStatusFinished)
	if err != nil {
		return
	}

	err = a.repo.DeleteDebts(ctx, userId, userType)

	return
}
