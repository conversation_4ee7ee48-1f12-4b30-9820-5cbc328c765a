package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util/provider/payme"

	"github.com/riverqueue/river"
)

func (a *App) RefillDriverBalanceTask(ctx context.Context, req model.RefillDriverBalanceRequest, attempt int) (err error) {
	var (
		payment        model.Payment
		receiptId      string
		receiptCreated bool
		isPaid         bool
		inProcess      bool
	)

	if attempt > 10 {
		return a.checkAndBlockDriverForLowBalance(ctx, req.DriverId)
	}

	card, err := a.repo.GetCardByUserId(ctx, req.DriverId, "driver")
	if err != nil {
		return
	}

	if card.Id == 0 || card.PaymeToken.String == "" {
		return a.checkAndBlockDriverForLowBalance(ctx, req.DriverId)
	}

	if attempt <= 1 {
		time.Sleep(10 * time.Second)
	} else {
		payment, err = a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, model.PaymentReasonAutorefillBalance)
		if err != nil || payment.IsFinished() {
			return
		}

		if payment.IsCreated() {
			receiptId = payment.Invoice
			receiptCreated, inProcess, isPaid, err = a.checkAndUpdateClientPaymePayment(ctx, receiptId)
			if err != nil || isPaid {
				return
			}
			if inProcess {
				if attempt < 3 {
					err = river.JobSnooze(60 * time.Second) // pay in process
				} else {
					err = errors.New("pay in process")
				}
				return
			}
		}
	}

	for {
		var balanceEnough bool
		balanceEnough, err = a.payme.CardBalanceLong(ctx, card.PaymeToken.String, req.Amount)
		if err != nil {
			switch err {
			case payme.ErrInsufficientBalance,
				payme.ErrCardNotFound,
				payme.ErrInvalidTokenFormat,
				payme.ErrCardExpired:
				return a.checkAndBlockDriverForLowBalance(ctx, req.DriverId)
			default:
				err = fmt.Errorf("check card balance: %v", err)
			}
			return
		}

		if balanceEnough {
			goto pay
		}

		if req.Amount < 2000 {
			return
		}

		req.Amount /= 2
		time.Sleep(100 * time.Millisecond)
	}

pay:

	if !receiptCreated {
		receiptId, err = a.payme.CreateReceipt(ctx, req.DriverId, req.OrderId, card.Id, req.Amount, "driver_refill", "Автопополнение баланса")
		if err != nil {
			err = fmt.Errorf("create receipt: %v", err)
			return
		}
	}

	state, err := a.payme.PayReceipt(ctx, card.PaymeToken.String, receiptId)
	if err != nil {
		switch err {
		case payme.ErrInsufficientBalance,
			payme.ErrCardNotFound,
			payme.ErrInvalidTokenFormat,
			payme.ErrCardExpired:
			return a.checkAndBlockDriverForLowBalance(ctx, req.DriverId)
		case payme.ErrReceiptsAlreadyPayed:
			err = nil
		default:
			err = fmt.Errorf("pay receipt: %v", err)
			return
		}
	} else if state.Int64 != payme.CheckStatusPaid {
		err = fmt.Errorf("pay check state not success: %d", state.Int64)
		return
	}

	return
}
