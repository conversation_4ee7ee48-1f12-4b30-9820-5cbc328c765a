package app

import (
	"context"
	"fmt"
	"strconv"

	"billing_service/model"
	"billing_service/util"
	"billing_service/util/provider/paynet"
)

func (a *App) PaynetWebhook(ctx context.Context, req paynet.WehookRequest) (resp paynet.WebhookResponse) {
	switch req.Method {
	case "GetInformation":
		driverId := util.ParseInt(req.Params.Fields.DriverID)

		// driverExsist, err := a.repo.GetDriverExists(ctx, driverId)
		// if err != nil {
		// 	resp.SetInternalError(err.Error())
		// 	return
		// }

		// if !driverExsist {
		// 	resp.SetDriverNotFoundError()
		// 	return
		// }

		driverInfo, err := a.repo.GetDriverInfo(ctx, driverId)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		if driverInfo.Id == 0 {
			resp.SetDriverNotFoundError()
			return
		}

		driverBalance, err := a.repo.GetDriverBalance(ctx, driverId)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		resp.SetGetInformationResponse(driverBalance.Balance, driverInfo.Name, req.ID)
		return

	case "PerformTransaction":
		driverId := util.ParseInt(req.Params.Fields.DriverID)

		driverExsist, err := a.repo.GetDriverExists(ctx, driverId)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		if !driverExsist {
			resp.SetDriverNotFoundError()
			return
		}

		invoice := strconv.Itoa(req.Params.TransactionID)
		amount := util.TiyinToSoum(req.Params.Amount)

		payment := model.Payment{
			UserId:     driverId,
			UserType:   "driver",
			Amount:     amount,
			Invoice:    invoice,
			Status:     model.PaymentStatusFinished,
			Reason:     model.PaymentReasonRefillBalance,
			ProviderId: model.ProviderIdPaynet,
		}

		err = a.repo.CreatePayment(ctx, payment)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		payment, err = a.repo.GetPaymentStatusByInvoice(ctx, invoice)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		msgId := fmt.Sprintf("driver:%d:balance_refill:%s", driverId, invoice)
		err = a.RefillDriverBalance(ctx, driverId, amount, 0, 0, "Пополнение баланса", msgId)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		a.repo.SendDriverNotification(driverId, 0, amount, msgId, "driver_balance_refilled")

		resp.SetPerformTransactionResponse(payment.Id, req.Params.Fields.DriverID, req.ID)
		return

	case "CheckTransaction":
		payment, err := a.repo.GetPaymentStatusByInvoice(ctx, strconv.Itoa(req.Params.TransactionID))
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		resp.SetCheckTransactionResponse(payment.Id, payment.Status, req.ID)
		return

	case "CancelTransaction":
		invoice := strconv.Itoa(req.Params.TransactionID)

		payment, err := a.repo.GetPaymentDetailsByInvoice(ctx, invoice)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		if payment.Status == model.PaymentStatusCancelled || payment.Status == model.PaymentStatusCancelledAfterFinish {
			// resp.SetTransactionAlreadyCancelledError()
			resp.SetCancelTransactionResponse(payment.Id, payment.Status, req.ID)
			return
		}

		status := model.PaymentStatusCancelledAfterFinish

		if payment.Status != model.PaymentStatusFinished {
			status = model.PaymentStatusCancelled
		}

		err = a.repo.UpdatePaymentStatus(ctx, invoice, status)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		if payment.Status == model.PaymentStatusFinished {
			reqId := fmt.Sprintf("driver:%d:paynet:%scancel", payment.UserId, payment.Invoice)
			err = a.RefineDriverBalance(ctx, payment.UserId, util.TiyinToSoum(payment.Amount), a.cfg.Defaults.BillingDeductionAccountNum, "Отмена пополнения баланса", reqId)
			if err != nil {
				resp.SetInternalError(err.Error())
				return
			}
		}

		resp.SetCancelTransactionResponse(payment.Id, status, req.ID)
		return

	case "GetStatement":
		payments, err := a.repo.GetPaynetPayments(ctx, req.Params.DateFrom, req.Params.DateTo)
		if err != nil {
			resp.SetInternalError(err.Error())
			return
		}

		resp.SetGetStatementResponse(payments, req.ID)
		return

	default:
		resp.SetMethodNotFoundError()
		return
	}
}
